# BM Essay Tutor - Environment Variables

# OpenRouter API Configuration
# Get your API key from https://openrouter.ai/
# Store in .env file and do not commit to git
OPENROUTER_API_KEY=your_api_key_here

# OpenRouter API Models
# Different models are used for different tasks to optimize performance
# Available models include:
# - anthropic/claude-3-opus (Most capable, highest cost)
# - anthropic/claude-3-sonnet (Balanced performance and cost)
# - anthropic/claude-3-haiku (Fastest, lowest cost)
# - openai/gpt-4 (Strong creative and reasoning capabilities)
# - openai/gpt-3.5-turbo (Fast and cost-effective)
# - google/gemini-pro (Good all-rounder)
# - google/gemini-1.5-pro (Enhanced capabilities)

# Translation model: Uses Claude 3 Opus for high-accuracy translations
OPENROUTER_MODEL_TRANSLATION=anthropic/claude-3-opus

# Ideation model: Uses GPT-4 for creative essay topic brainstorming
OPENROUTER_MODEL_IDEATION=openai/gpt-4

# General questions model: Uses Claude 3 Sonnet for balanced performance
OPENROUTER_MODEL_QUESTIONS=anthropic/claude-3-sonnet

# Essay marking model: Uses Claude 3 Opus for detailed analytical marking
OPENROUTER_MODEL_MARKER=anthropic/claude-3-opus

# OpenRouter Base URL
OPENROUTER_BASE_URL=https://api.openrouter.ai/api/v1/chat/completions
