{"name": "magic-patterns-vite-template", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "npx vite", "build": "npx vite build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "preview": "npx vite preview"}, "dependencies": {"axios": "^1.9.0", "dotenv": "^16.5.0", "html2pdf.js": "^0.10.3", "lucide-react": "^0.441.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0"}, "devDependencies": {"@types/node": "^20.11.18", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "latest", "eslint": "^8.50.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "postcss": "latest", "tailwindcss": "3.4.17", "typescript": "^5.5.4", "vite": "^5.2.0"}}