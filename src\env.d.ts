/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_OPENROUTER_API_KEY: string
  readonly VITE_OPENROUTER_MODEL_TRANSLATION: string
  readonly VITE_OPENROUTER_MODEL_IDEATION: string
  readonly VITE_OPENROUTER_MODEL_QUESTIONS: string
  readonly VITE_OPENROUTER_MODEL_MARKER: string
  readonly VITE_OPENROUTER_BASE_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
