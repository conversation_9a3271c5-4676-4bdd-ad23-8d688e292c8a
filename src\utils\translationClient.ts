import axios from 'axios';
import { logger } from './debugLogger';

interface OpenRouterResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

interface OpenRouterRequest {
  messages: {
    role: 'system' | 'user' | 'assistant';
    content: string;
  }[];
}

const systemPrompt = `You are a Year 5 Malay tutor. The student will ask for an English→Malay translation.
1. NEVER reveal the full answer in a hint. You can only reveal the full answer after the student has given up or after 3 incorrect attempts.
2. Provide percentage‑based reveals of the target word over three attempts:
   - **First hint (50% reveal):** display half the letters in their correct positions.
   - **Second hint (75% reveal):** display three‑quarters of the letters.
   - **Third hint (90% reveal):** display ninety percent of the letters.
2. After the third incorrect attempt, reveal the complete word.

Example (word: "memberi", 7 letters):
- AI: "Here's a hint, it starts with mem and has 7 letters"
- Student: "membeli?"
- AI: "Not correct. Here's a second hint: It start with membe
- Student: "member?"
- AI: "Still not correct. Final hint!: it start with member"
- Student: "I give up."
- AI: "The word is 'memberi'."`;

export const getTranslationResponse = async (
  prompt: string,
  previousMessages: { role: 'user' | 'assistant'; content: string }[] = []
): Promise<string> => {
  logger.info('Translation', '🚀 Starting new translation request', { prompt });
  
  if (!prompt) {
    logger.warn('Translation', '⚠️ Empty prompt received');
    return '';
  }

  const apiKey = import.meta.env.VITE_OPENROUTER_API_KEY;
  if (!apiKey) {
    logger.error('Translation', '❌ API key not found in environment variables');
    return 'Sorry, I cannot help right now. Please check the API configuration.';
  }

  try {
    // Construct conversation history
    const messages: OpenRouterRequest['messages'] = [
      { role: 'system', content: systemPrompt },
      ...previousMessages,
      { role: 'user', content: prompt }
    ];    logger.info('Translation', '📝 Using conversation history', {
      messageCount: messages.length,
      lastUserMessage: prompt,
      fullHistory: messages
    });

    // Select model based on activity type
    const model = import.meta.env.VITE_OPENROUTER_MODEL_TRANSLATION;
    logger.info('Translation', '🤖 Selected model for translation', { model });

    const response = await axios.post<OpenRouterResponse>(
      import.meta.env.VITE_OPENROUTER_BASE_URL,
      {
        messages,
        model,
        temperature: 0.2,
        max_tokens: 150
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );    logger.info('Translation', '✅ Received API response', {
      status: response.status,
      hasChoices: response.data.choices?.length > 0,
      headers: response.headers
    });

    const content = response.data.choices[0].message.content;
    logger.info('Translation', '📤 Processing response content', {
      contentLength: content.length,
      preview: content.substring(0, 50) + '...',
      fullResponse: response.data
    });

    return content;
  } catch (error) {    if (axios.isAxiosError(error)) {
      logger.error('Translation', '🔴 Network or API error', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        }
      });
    } else {
      logger.error('Translation', '🔴 Unexpected error', {
        error: error instanceof Error ? error.message : error
      });
    }
    return 'Sorry, I encountered an error. Please try again later.';
  }
};
