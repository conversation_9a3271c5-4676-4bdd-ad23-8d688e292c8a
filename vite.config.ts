import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isDev = mode === 'development';
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    plugins: [react()],
    // Configuration for development mode
    server: {
      hmr: true,
       allowedHosts: [
      'f113-180-74-174-157.ngrok-free.app'
    ],
      // Enable more detailed logging in development
      ...(isDev && {
        clearScreen: false,
        logLevel: 'info',
      })
    },
    // Enable debug logging in development
    define: {
      __DEV__: isDev,
      // Expose env variables to the client
      'import.meta.env.VITE_OPENROUTER_API_KEY': JSON.stringify(env.OPENROUTER_API_KEY),
      'import.meta.env.VITE_OPENROUTER_MODEL_TRANSLATION': JSON.stringify(env.OPENROUTER_MODEL_TRANSLATION),
      'import.meta.env.VITE_OPENROUTER_MODEL_IDEATION': JSON.stringify(env.OPENROUTER_MODEL_IDEATION),
      'import.meta.env.VITE_OPENROUTER_MODEL_QUESTIONS': JSON.stringify(env.OPENROUTER_MODEL_QUESTIONS),
      'import.meta.env.VITE_OPENROUTER_MODEL_MARKER': JSON.stringify(env.OPENROUTER_MODEL_MARKER),
      'import.meta.env.VITE_OPENROUTER_BASE_URL': JSON.stringify(env.OPENROUTER_BASE_URL),
    }
  }
})
